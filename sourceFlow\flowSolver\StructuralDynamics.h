#ifndef _sourceFlow_flowSolver_StructuralDynamics_
#define _sourceFlow_flowSolver_StructuralDynamics_

#include "basic/common/CommonInclude.h"
#include "basic/geometry/Vector.h"
#include "basic/geometry/Matrix.h"

// 前向声明
template<class Type> class ElementField;

/**
 * @brief 结构动力学类
 * 用于求解广义结构运动方程，处理模态坐标系统
 */
class StructuralDynamics
{
public:
    /**
     * @brief 构造函数
     */
    StructuralDynamics();
    
    /**
     * @brief 析构函数
     */
    ~StructuralDynamics();
    
    /**
     * @brief 初始化结构动力学系统
     * @param wallPerturbationFields 物面摄动场数据（包含模态振型）
     * @param frequencies 各模态的固有频率
     */
    void Initialize(const std::vector<std::vector<Vector>>& wallPerturbationFields,
                   const std::vector<double>& frequencies);
    
    /**
     * @brief 求解广义结构运动方程
     * 使用Newmark-β方法求解 [M]{q̈} + [C]{q̇} + [K]{q} = {Q}
     * @param generalizedForces 广义气动力向量
     * @param timeStep 时间步长
     */
    void SolveGeneralizedEquation(const std::vector<double>& generalizedForces, double timeStep);
    
    /**
     * @brief 获取广义位移
     * @return 广义位移向量
     */
    const std::vector<double>& GetGeneralizedDisplacement() const;
    
    /**
     * @brief 获取广义速度
     * @return 广义速度向量
     */
    const std::vector<double>& GetGeneralizedVelocity() const;
    
    /**
     * @brief 获取广义加速度
     * @return 广义加速度向量
     */
    const std::vector<double>& GetGeneralizedAcceleration() const;
    
    /**
     * @brief 获取模态数量
     * @return 模态数量
     */
    int GetNumberOfModes() const;
    
    /**
     * @brief 获取模态振型数据（已废弃，使用摄动场数据）
     * @param modeIndex 模态索引
     * @param point 空间点坐标
     * @return 该点的模态振型向量
     */
    Vector GetModeShapeAtPoint(int modeIndex, const Vector& point) const;
    
    /**
     * @brief 设置结构阻尼参数
     * @param dampingRatio 阻尼比向量
     */
    void SetDampingRatio(const std::vector<double>& dampingRatio);
    
    /**
     * @brief 设置Newmark-β参数
     * @param beta β参数
     * @param gamma γ参数
     */
    void SetNewmarkParameters(double beta, double gamma);

private:
    /**
     * @brief 构建广义质量、刚度、阻尼矩阵
     */
    void BuildGeneralizedMatrices();

private:
    // 模态参数
    int numModes;                           ///< 模态数量
    std::vector<double> naturalFrequencies; ///< 固有频率 (rad/s)
    std::vector<double> dampingRatios;      ///< 阻尼比
    
    // 广义坐标和导数
    std::vector<double> generalizedDisp;    ///< 广义位移 {q}
    std::vector<double> generalizedVel;     ///< 广义速度 {q̇}
    std::vector<double> generalizedAcc;     ///< 广义加速度 {q̈}
    
    // 上一时间步的状态
    std::vector<double> generalizedDispOld; ///< 上一步广义位移
    std::vector<double> generalizedVelOld;  ///< 上一步广义速度
    std::vector<double> generalizedAccOld;  ///< 上一步广义加速度
    
    // 广义矩阵（对角化后）
    std::vector<double> generalizedMass;    ///< 广义质量矩阵对角元素
    std::vector<double> generalizedStiff;   ///< 广义刚度矩阵对角元素
    std::vector<double> generalizedDamp;    ///< 广义阻尼矩阵对角元素
    
    // Newmark-β参数
    double newmarkBeta;   ///< β参数，默认0.25
    double newmarkGamma;  ///< γ参数，默认0.5
    
    // 时间积分相关
    double currentTime;   ///< 当前时间
    bool isInitialized;   ///< 初始化标志
};

#endif
