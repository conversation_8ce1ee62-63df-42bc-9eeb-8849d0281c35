﻿//////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file MotionManager.h
//! <AUTHOR>
//! @brief MotionManager为网格运动管理类
//! @date  2023-10-7
//
//------------------------------修改日志----------------------------------------
//
// 2023-10-7 曾凯
// 说明：建立
// 
//------------------------------------------------------------------------------

#ifndef _meshProcess_Motion_MotionManager_
#define _meshProcess_Motion_MotionManager_

#include "sourceFlow/package/FlowPackage.h"
#include "meshProcess/motion/TranslationRotation.h"
#include "meshProcess/motion/Stationary.h"
#include "meshProcess/motion/SixdofCouple.h"


class MotionManager
{
public:
	/**
	* @brief constructor
	*
	*/
	MotionManager(std::vector<Package::FlowPackage *> flowPackageVector_);

	/**
	* @brief destructor
	*
	*/
	~MotionManager();

	/**
	* @brief 根据输入参数创建运动对象
	*
	*/
	void CreateMotions();

	/**
	* @brief 获取指定编号的运动对象
	*
	*/
	const Motion* GetMotion(int motionID);

	void MeshUpdate();

#if defined(_EnableFlutter_)
	/**
	 * @brief 使用广义位移更新颤振网格
	 * @param generalizedDisp 广义位移向量
	 */
	void UpdateFlutterMesh(const std::vector<double>& generalizedDisp);

	/**
	 * @brief 使用物理位移更新颤振网格
	 * @param physicalDisplacements 物理位移向量
	 */
	void UpdateFlutterMeshWithDisplacements(const std::vector<Vector>& physicalDisplacements);
#endif

private:
	Mesh *localMesh; //当前进程网格指针
	const Configure::Flow::FlowConfigure &flowConfig;
	std::vector<Package::FlowPackage *> flowPackageVector;

	int myZoneID;

	std::vector<Motion*> motions;





private:

	


};



#endif
