cmake_minimum_required (VERSION 2.8.8)
project (ARI_FeiLian C CXX)

#-----------------------------------------------------------------------------
# 设置ARI_FeiLian软件版本信息
#-----------------------------------------------------------------------------
# 版本信息
file(STRINGS "VERSION" lines)
list(GET lines 0 ARI_VERSION)
set(ARI_SRCDIR    "${PROJECT_SOURCE_DIR}")

# 定义软件逻辑版本和动态库版本
set(ARI_TARGET_VERSION ${ARI_VERSION} CACHE STRING "FeiLian logical version")
set(ARI_TARGET_SOVERSION ${ARI_VERSION} CACHE STRING "FeiLian soname version")
add_definitions(-DCAFFE_VERSION=${ARI_TARGET_VERSION})

#-----------------------------------------------------------------------------
# 设置工程控制参数
#-----------------------------------------------------------------------------
# 并行模式控制选项
option(ARI_DEVELOP_MODE         "Enable or disable develop mode" ON)
option(ARI_ENABLE_MPI           "Enable or disable MPI" ON)
option(ARI_ENABLE_OPENMP        "Use OpenMP" ON)

# 第三方库控制选项
option(ARI_ENABLE_EIGEN         "Enable or disable Eigen" ON)
option(ARI_ENABLE_HDF5          "Enable or disable HDF5" ON)
option(ARI_ENABLE_CGNS          "Enable or disable CGNS" ON)
option(ARI_ENABLE_METIS         "Enable or disable Metis" ON)
option(ARI_ENABLE_PARMGRIDGEN   "Enable or disable ParMGridGen" ON)
option(ARI_ENABLE_PETSc         "Enable or disable PETSc" OFF)
option(ARI_ENABLE_TECIO         "Enable or disable TecIO" ON)
option(ARI_ENABLE_ONNXRUNTIME   "Enable or disable onnxRuntime" OFF)

# 软件功能控制选项
option(ARI_ENABLE_OVERSET       "Enable or disable Overset" OFF)
option(ARI_ENABLE_MKL           "Enable or disable MKL" OFF)
option(ARI_ENABLE_MESHDEFORM    "Enable or disable MeshDeform" OFF)
option(ARI_ENABLE_AEROSTATIC    "Enable or disable StaticAeroelastic" OFF)
option(ARI_ENABLE_MLTURBMODEL   "Enable or disable MLTurbModel" OFF)
option(ARI_ENABLE_MULTISPECIES  "Enable or disable MultiSpecies" OFF)
option(ARI_ENABLE_PARTICLE      "Enable or disable Particle" OFF)

# 共享库编译选项
option(ARI_BUILD_SHARED         "Build a shared library" OFF)

# 自定义库控制选项
option(ARI_BUILD_BASIC          "Build basic libraries" ON)
option(ARI_BUILD_MESHPROCESS    "Build meshProcess libraries" ON)
option(ARI_BUILD_SOURCEFLOW     "Build sourceFlow libraries" ON)
option(ARI_BUILD_SOURCEICING    "Build sourceIcing libraries" OFF)

# 可执行文件编译选项
option(ARI_BUILD_TOOLS          "Build basic tools" ON)
option(ARI_BUILD_FLOWSOLVER     "Build flow solver" ON)

# 气动弹性编译选项，自动打开动网格
if(ARI_ENABLE_AEROSTATIC)
    option(ARI_ENABLE_MESHDEFORM    "Enable or disable MeshDeform" ON)
endif()

# 动网格编译选项，自动打开MKL
if(ARI_ENABLE_MESHDEFORM)
    option(ARI_ENABLE_MKL           "Enable or disable MKL" ON)
endif()

#-----------------------------------------------------------------------------
# 设置工程信息
#-----------------------------------------------------------------------------
# 源码编译支持c++0x或c++11，使用机器学习(onnxruntime)时要求c++17
include(CheckCXXCompilerFlag)
CHECK_CXX_COMPILER_FLAG("-std=c++17" COMPILER_SUPPORTS_CXX17)
CHECK_CXX_COMPILER_FLAG("-std=c++11" COMPILER_SUPPORTS_CXX11)
CHECK_CXX_COMPILER_FLAG("-std=c++0x" COMPILER_SUPPORTS_CXX0X)
if(COMPILER_SUPPORTS_CXX17 AND ARI_ENABLE_ONNXRUNTIME)
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -std=c++17")
elseif(COMPILER_SUPPORTS_CXX11)
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -std=c++11")
elseif(COMPILER_SUPPORTS_CXX0X)
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -std=c++0x")
else()
    message(STATUS "The compiler has no C++11 support.")
endif()

# 采用folder组织工程
set_property(GLOBAL PROPERTY USE_FOLDERS ON)

#预定义的Target放入此文件中（如ALL_BUILD等）
set_property(GLOBAL PROPERTY PREDEFINED_TARGETS_FOLDER "_CMakeTargets_")

# 检查源码路径是否与工程路径相同
if (${ARI_SOURCE_DIR} STREQUAL ${ARI_BINARY_DIR})
    message(FATAL_ERROR "In-place build not allowed!")
endif ()

#-----------------------------------------------------------------------------
# 配置编译环境
#-----------------------------------------------------------------------------
# 打印编译环境信息
message(STATUS "Operation system: ${CMAKE_SYSTEM}")
message(STATUS "System name: ${CMAKE_SYSTEM_NAME}")

# 定义编译环境系统变量
if (CMAKE_SYSTEM_NAME MATCHES "Linux")
    add_definitions(-D_BasePlatformLinux_)
elseif (CMAKE_SYSTEM_NAME MATCHES "Windows")
    add_definitions(-D_BasePlatformWinddows_)
else ()
    message(STATUS "Other platform: ${CMAKE_SYSTEM_NAME}")
endif ()

# 定义开发环境变量
if(ARI_DEVELOP_MODE)
    add_definitions(-D_DevelopMode_)
endif ()

# 设置编译及优化类型
set(ARI_BUILD_TYPE "Release" CACHE STRING
  "Optimization flags: set to Debug, Release, RelWithDebInfo, or MinSizeRel")
set(CMAKE_BUILD_TYPE "${ARI_BUILD_TYPE}" CACHE INTERNAL "" FORCE)

# 编译过程显示详细编译信息
if (CMAKE_SYSTEM_NAME MATCHES "Linux")
set(CMAKE_VERBOSE_MAKEFILE OFF)
endif ()

# MSVC编译设置
if (MSVC)
    # 采用MSVC时定义变量CRT_SECURE_NO_WARNINGS
    add_definitions(-D_CRT_SECURE_NO_WARNINGS)
    
    # Release下的调试模式设置
    if (("${ARI_BUILD_TYPE}" STREQUAL "Release") AND ARI_DEVELOP_MODE)
        add_definitions( /Zi )
        add_definitions( /Od )
        set(CMAKE_EXE_LINKER_FLAGS_RELEASE "${CMAKE_EXE_LINKER_FLAGS_RELEASE} /DEBUG" CACHE INTERNAL "" FORCE)
        set(CMAKE_SHARED_LINKER_FLAGS_RELEASE "${CMAKE_SHARED_LINKER_FLAGS_RELEASE} /DEBUG" CACHE INTERNAL "" FORCE)
    endif()
    
    #抑制warning
    add_compile_options(/wd4819) #当前代码页(936)中表示的字符
    add_compile_options(/wd4267) #从“size_t”转换到“int”
    add_compile_options(/wd4503) #超长修饰
    add_compile_options(/wd4005) #宏重定义

    ##支持UTF-8
    #add_compile_options("$<$<CXX_COMPILER_ID:MSVC>:/source-charset:utf-8>")
    #add_compile_options("$<$<CXX_COMPILER_ID:MSVC>:/execution-charset:utf-8>")
elseif (UNIX)
    # Release下的不调试
    if("${ARI_BUILD_TYPE}" STREQUAL "Release")
        set(CMAKE_CXX_FLAGS_RELEASE "${CMAKE_CXX_FLAGS_RELEASE} -O3")
        set(CMAKE_C_FLAGS_RELEASE "${CMAKE_C_FLAGS_RELEASE} -O3")
        set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -O3 -Wall")
        set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -O3 -Wall")
    endif()

    # 编译选项
	add_compile_options(-ldl)
	add_compile_options(-lz)
	add_compile_options(-fpermissive)
    
    # 抑制warning
    if (CMAKE_CXX_COMPILER_ID MATCHES "GNU")
    #add_compile_options(-Wno-deprecated-decarations)
    #add_compile_options(-Wno-return-type)
    add_compile_options(-Wno-sign-compare)
    add_compile_options(-Wno-reorder)
	if(COMPILER_SUPPORTS_CXX11)
		add_compile_options(-Wno-unused-result)
		add_compile_options(-Wno-unused-value)
		add_compile_options(-Wno-unused-variable)
		add_compile_options(-Wno-delete-incomplete)
		add_compile_options(-Wno-misleading-indentation)
	endif ()
    elseif (CMAKE_CXX_COMPILER_ID MATCHES "Intel")
        set(CMAKE_CXX_FLAGS_RELEASE "${CMAKE_CXX_FLAGS_RELEASE} -wd858 -wd1011")
        set(CMAKE_C_FLAGS_RELEASE "${CMAKE_C_FLAGS_RELEASE} -wd858 -wd1011")
        set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -wd858 -wd1011")
        set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -wd858 -wd1011")
    endif()

endif ()

if ("${ARI_BUILD_TYPE}" STREQUAL "Debug")
    add_definitions(-D_DebugMode_)
    message(STATUS "Build type: ${ARI_BUILD_TYPE} ${CMAKE_CXX_FLAGS_DEBUG}")
else ()
    add_definitions(-D_ReleaseMode_)
    message(STATUS "Build type: ${ARI_BUILD_TYPE} ${CMAKE_CXX_FLAGS_RELEASE}")
endif ()

#-----------------------------------------------------------------------------
# 配置安装环境
#-----------------------------------------------------------------------------
# 设置安装路径
set(ARI_INSTALL_PREFIX ${PROJECT_SOURCE_DIR}/Release/FeiLian_${ARI_VERSION} CACHE PATH
  "Installation directory for FeiLian")
set(CMAKE_INSTALL_PREFIX ${ARI_INSTALL_PREFIX} CACHE INTERNAL "" FORCE)

## 安装doc
#install(DIRECTORY ${PROJECT_SOURCE_DIR}/doc DESTINATION ${ARI_INSTALL_PREFIX})

# 安装parameter
install(DIRECTORY ${PROJECT_SOURCE_DIR}/parameter DESTINATION ${ARI_INSTALL_PREFIX})

# 打印安装路径
message(STATUS "Install path: ${CMAKE_INSTALL_PREFIX}")

#-----------------------------------------------------------------------------
# 依次添加第三方库、功能模块、求解器基础库、流场求解库、网格处理库、可执行文件等
#-----------------------------------------------------------------------------
# 可执行文件输出路径
set(EXECUTABLE_OUTPUT_PATH ${PROJECT_SOURCE_DIR}/bin)
execute_process( COMMAND ${CMAKE_COMMAND} -E make_directory ${EXECUTABLE_OUTPUT_PATH})

# 添加第三方库
set(EXTERNAL_PATH ${PROJECT_SOURCE_DIR}/feilian-external)
include(${EXTERNAL_PATH}/third_part_libs.cmake)

# 编译功能模块
if(ARI_ENABLE_OVERSET)
    add_subdirectory(feilian-specialmodule/oversetMesh)
    add_definitions(-D_EnableOverset_)
else()
    add_definitions(-D_DisableOverset_)
endif()

if(ARI_ENABLE_MESHDEFORM)
    # add_subdirectory(feilian-specialmodule/meshDeform)
    add_definitions(-D_EnableMeshDeform_)
endif()

if(ARI_ENABLE_AEROSTATIC)
    add_subdirectory(feilian-specialmodule/staticAeroelastic)
    add_definitions(-D_EnableStaticAeroelastic_)
endif()

if(ARI_ENABLE_MLTURBMODEL)
    add_subdirectory(feilian-specialmodule/mlTurbModel)
    add_definitions(-D_EnableMLTurbModel_)
endif()

if(ARI_ENABLE_MULTISPECIES)
  add_subdirectory(feilian-specialmodule/multiSpecies)
  add_definitions(-D_EnableMultiSpecies_)
endif(ARI_ENABLE_MULTISPECIES)

if(ARI_ENABLE_PARTICLE)
  add_subdirectory(feilian-specialmodule/particle)
  add_definitions(-D_EnableParticle_)
endif(ARI_ENABLE_PARTICLE)

# 编译类型
if(ARI_BUILD_SHARED)
    set(BUILD_TYPE SHARED)
else()
    set(BUILD_TYPE STATIC)
endif()

# 添加求解器基础库
if(ARI_BUILD_BASIC)
    add_subdirectory(basic)
endif(ARI_BUILD_BASIC)

# 添加网格处理库
if(ARI_BUILD_MESHPROCESS)
    add_subdirectory(meshProcess)
endif(ARI_BUILD_MESHPROCESS)

# 添加流场求解库
if(ARI_BUILD_SOURCEFLOW)
    add_subdirectory(sourceFlow)
endif(ARI_BUILD_SOURCEFLOW)

# 添加可执行文件
add_subdirectory(ARI_FeiLian)

