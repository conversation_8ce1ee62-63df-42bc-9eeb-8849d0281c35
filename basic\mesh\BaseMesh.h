////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file BaseMesh.h
//! <AUTHOR>
//! @brief 基本网格类
//! @date 2020-07-27
//
//------------------------------修改日志----------------------------------------
// 2021-12-31 李艳亮、乔龙（气动院）
//    说明：调整并规范化。
//
// 2020-07-27 凌空
//    说明：建立。
//------------------------------------------------------------------------------
#ifndef _basic_mesh_BaseMesh_
#define _basic_mesh_BaseMesh_

#include "basic/configure/ConfigureMacro.h"
#include "basic/configure/ConfigureMacro.hxx"
#include "basic/common/ListTools.h"
#include "basic/mesh/MeshSupport.h"
#include "basic/mesh/MultiStructuredBlock.h"

// 用于友元类的声明（暂时）
class DecomposeManager;
class AgglomerateManager;
class AgglomerateStructuredMesh;
class DualMesh;
class MeshSorting;
class WallDistanceBase;
class WallDistanceManager;
class MeshProcess;
class CgnsMesh;
class CgnsMeshStructured;
class DlgMesh;
class FluentMeshBlock;
class MeshConverter;
class MeshConvertManager;
class SubMesh;
class OversetMesh;

/**
 * @class BaseMesh
 * @brief 网格基础类
 * @details 提供网格数据存储、查询、变换等基础功能，是所有网格类型的基类
 */
class BaseMesh
{
public:    
    /**
     * @enum MeshDim
     * @brief 网格维度枚举
     */
    enum MeshDim 
    {
        md2D    = 2,    ///< 二维网格
        md3D    = 3,    ///< 三维网格
        mdNoType        ///< 未定义类型
    };    
    
    /**
     * @enum MeshIOState
     * @brief 网格IO状态枚举
     */
    enum MeshIOState 
    {
        READ,   ///< 读取状态
        WRITE   ///< 写入状态
    };

public: // 构造函数
    /**
     * @brief 默认构造函数
     */
    BaseMesh();
    
    /**
     * @brief 构造函数(通过网格文件名)
     * @param MshFileName 网格文件名
     */
    BaseMesh(const std::string& MshFileName);
    
public: // 网格基本信息访问接口
    /**
     * @brief 获取网格维度
     * @return 网格维度枚举值
     */
    const MeshDim &GetMeshDimension()const { return this->md_meshDim; }
    
    /**
     * @brief 获取网格名称
     * @return 网格名称字符串
     */
    const std::string &GetMeshName()const { return this->st_meshName; }
    
    /**
     * @brief 设置网格名称
     * @param name 新网格名称
     */
    void SetMeshName(const std::string &name) { this->st_meshName = name; }
    
    /**
     * @brief 获取网格文件名
     * @return 文件名字符串
     */
    const std::string &GetFileName()const { return this->st_fileName; }
    
    /**
     * @brief 设置网格文件名
     * @param name 新文件名
     */
    void SetFileName(const std::string &name) { this->st_fileName = name; }
    
    /**
     * @brief 获取单元形状类型
     * @return 单元形状类型枚举值
     */
    const Element::ElemShapeType &GetElemShapeType()const { return this->est_shapeType; }
    
    /**
     * @brief 获取所有单元数量(含虚单元)
     * @return 单元总数
     */
    const int &GetElementNumberAll()const  {return this->n_elemNum_all;}
    
    /**
     * @brief 获取实单元数量
     * @return 实单元数
     */
    const int &GetElementNumberReal()const  {return this->n_elemNum;}
    
    /**
     * @brief 获取面数量
     * @return 面总数
     */
    const int &GetFaceNumber()const  { return this->n_faceNum; }
    
    /**
     * @brief 获取节点数量
     * @return 节点总数
     */
    const int &GetNodeNumber()const  { return this->n_nodeNum; }
    
    /**
     * @brief 获取指定单元
     * @param elementID 单元ID
     * @return 单元对象引用
     * @throw std::out_of_range 当elementID超出范围时抛出
     */
    const Element &GetElement(const int &elementID)const {return this->v_elem[elementID];}
    
    /**
     * @brief 获取指定面
     * @param faceID 面ID
     * @return 面对象引用
     * @throw std::out_of_range 当faceID超出范围时抛出
     */
    const Face &GetFace(const int &faceID)const { return this->v_face[faceID]; }
    
    /**
     * @brief 获取指定节点
     * @param nodeID 节点ID
     * @return 节点对象引用
     * @throw std::out_of_range 当nodeID超出范围时抛出
     */
    const Node &GetNode(const int &nodeID)const { return this->v_node[nodeID]; }
    
public: // 边界相关接口
    /**
     * @brief 获取边界数量
     * @return 边界总数
     */
    const int GetBoundarySize()const { return (int)this->vv_boundaryFaceID.size(); }
    
    /**
     * @brief 获取指定边界的面数量
     * @param patchID 边界ID
     * @return 该边界包含的面数
     * @throw std::out_of_range 当patchID超出范围时抛出
     */
    const int GetBoundaryFaceSize(const int &patchID)const { return (int)this->vv_boundaryFaceID[patchID].size(); }
    
    /**
     * @brief 获取边界面的全局ID
     * @param patchID 边界ID
     * @param index 面在边界中的索引
     * @return 面的全局ID
     * @throw std::out_of_range 当patchID或index超出范围时抛出
     */
    const int &GetBoundaryFaceID(const int &patchID, const int &index)const {return this->vv_boundaryFaceID[patchID][index];}
    
    /**
     * @brief 获取边界名称
     * @param patchID 边界ID
     * @return 边界名称字符串
     * @throw std::out_of_range 当patchID超出范围时抛出
     */
    const std::string &GetBoundaryName(const int &patchID)const { return this->v_boundaryName[patchID]; }    

public: // 网格数据修改接口
    /**
     * @brief 设置节点数据
     * @param nodeID 节点ID
     * @param node 节点对象
     * @throw std::out_of_range 当nodeID超出范围时抛出
     */
    void SetNode(const int &nodeID, const Node &node) { this->v_node[nodeID] = node; }
    
    /**
     * @brief 设置单元中心
     * @param elemID 单元ID
     * @param center 中心坐标
     * @throw std::out_of_range 当elemID超出范围时抛出
     */
    void SetElementCenter(const int &elemID, const Vector &center) { this->v_elem[elemID].center = center; }
    
    /**
     * @brief 设置单元体积
     * @param elemID 单元ID
     * @param volume 体积值
     * @throw std::out_of_range 当elemID超出范围时抛出
     */
    void SetElementVolume(const int &elemID, const Scalar &volume) { this->v_elem[elemID].volume = volume; }
    
    /**
     * @brief 设置面中心
     * @param faceID 面ID
     * @param center 中心坐标
     * @throw std::out_of_range 当faceID超出范围时抛出
     */
    void SetFaceCenter(const int &faceID, const Vector &center) { this->v_face[faceID].center = center; }
    
    /**
     * @brief 设置面法向量
     * @param faceID 面ID
     * @param normal 法向量
     * @throw std::out_of_range 当faceID超出范围时抛出
     */
    void SetFaceNormal(const int &faceID, const Vector &normal) { this->v_face[faceID].normal = normal; }
    
    /**
     * @brief 获取边界的节点ID列表
     * @param[in] patchID 边界编号
     * @param[out] v_boundaryNodeID 当前边界上的节点ID集合
     * @throw std::out_of_range 当patchID超出范围时抛出
     */
    void PopulateBoundaryNodeID(const int &patchID, std::vector<int> &v_boundaryNodeID);
    
    /**
     * @brief 获取边界的节点ID列表及面的局部构成
     * @param[in] patchID 边界编号
     * @param[out] v_boundaryNodeID 当前边界上的节点ID集合
     * @param[out] vv_boundaryFaceNodeID 当前边界上的每个面的节点构成(节点编号为当前边界内所有节点的局部编号)
     * @throw std::out_of_range 当patchID超出范围时抛出
     */
    void PopulateBoundaryFaceNodeID(const int &patchID, std::vector<int> &v_boundaryNodeID, 
                                  std::vector<std::vector<int>> &vv_boundaryFaceNodeID);
                
public: // 网格域相关接口
    /**
     * @brief 获取网格域ID
     * @return 网格域ID
     */
    const int &GetMeshZoneID()const {return this->zoneID; }
    
    /**
     * @brief 设置网格域ID
     * @param zoneID_ 新的网格域ID
     */
    void SetMeshZoneID(const int &zoneID_) {this->zoneID = zoneID_; }

public: // 结构化网格块相关接口
    /**
     * @brief 获取指定块
     * @param ID 块ID
     * @return 块对象引用
     * @throw std::out_of_range 当ID超出范围时抛出
     */
    const Block &GetBlock(const int &ID)const {return multiStructuredBlock.GetBlock(ID);}
    
    /**
     * @brief 获取指定连接
     * @param ID 连接ID
     * @return 连接对象引用
     * @throw std::out_of_range 当ID超出范围时抛出
     */
    const Connection &GetConnection(const int &ID)const {return multiStructuredBlock.GetConnection(ID);}
    
    /**
     * @brief 添加块
     * @param block_ 要添加的块对象
     */
    void AddBlock(const Block &block_) { multiStructuredBlock.AddBlock(block_); }
    
    /**
     * @brief 添加连接
     * @param connection_ 要添加的连接对象
     */
    void AddConnection(const Connection &connection_) {multiStructuredBlock.AddConnection(connection_);}
    
    /**
     * @brief 设置节点索引
     */
    void SetNodeIndex() { multiStructuredBlock.SetNodeIndex(true); }
    
    /**
     * @brief 获取节点总数
     * @return 节点总数
     */
    int GetNodeTotalSize() { return multiStructuredBlock.GetNodeTotalSize(); }

    /**
     * @brief 判断是否为结构网格
     * @return 结构网格标识
     */
	bool JundgeStructured() { return multiStructuredBlock.GetBlockSize() > 0; }

public: // 网格变换操作
    /**
     * @brief 网格缩放(各向同性)
     * @param scale 缩放比例
     */
    void Scale(Scalar scale);
    
    /**
     * @brief 网格缩放(各向异性)
     * @param scale 各方向缩放比例向量
     */
    void Scale(Vector scale);
    
    /**
     * @brief 网格平移
     * @param translation 平移向量
     */
    void Translate(Vector translation);
    
    /**
     * @brief 网格旋转
     * @param axis 旋转轴向量
     * @param center 旋转中心
     * @param angle 旋转角度(弧度)
     */
    void Rotate(Vector axis, Vector center, Scalar angle);
    
    /**
     * @brief 清空网格数据
     */
    void ClearMesh();
    
    /**
     * @brief 打印网格信息
     */
    void PrintMeshInfomation();
    
public: // 网格查询操作
    /**
     * @brief 查询单元邻居
     * @param EID 单元ID
     * @return 邻居单元ID列表
     * @throw std::out_of_range 当EID超出范围时抛出
     */
    std::vector<int> SearchElementNeighbor(int EID) const;
    
    /**
     * @brief 查询距离范围内的单元
     * @param EID 中心单元ID
     * @param dis 搜索距离
     * @return 范围内单元ID列表
     * @throw std::out_of_range 当EID超出范围时抛出
     */
    std::vector<int> SearchNearbyElements(int EID, Scalar dis) const;
    
    /**
     * @brief 查询指定层数的邻居单元
     * @param EID 中心单元ID
     * @param layerNum 搜索层数
     * @return 范围内单元ID列表
     * @throw std::out_of_range 当EID超出范围时抛出
     */
    std::vector<int> SearchNearbyElements(int EID, int layerNum) const;
    
public: // 网格计算操作
    /**
     * @brief 计算单元中心和体积
     * @param boundaryFlag 仅计算边界标识
     */
    void CalculateCenterAndVolume(const bool boundaryFlag = false);
    
    /**
     * @brief 检查面方向一致性
     */
    void CheckFaceDirection();
    
    /**
     * @brief 获取特征长度
     * @return 特征长度值
     */
    Scalar GetCharacteristicLength() const;   

    /**
     * @brief 更新单元面ID
     */
    void UpdateElementFaceID();
    
protected:
    int zoneID;                                       ///< 当前网格所属网格域编号
    Element::ElemShapeType est_shapeType;             ///< 构成网格的单元类型
    int    n_elemNum;                                 ///< 实单元数量
    int    n_faceNum;                                 ///< 面数量
    int    n_nodeNum;                                 ///< 点数量
    int    n_elemNum_all;                             ///< 所有单元数量(含虚单元)
                  
    MeshDim    md_meshDim;                            ///< 网格维度
    std::string st_fileName;                          ///< 网格文件名称,根据文件类型补上后缀后使用
    std::string st_meshName;                          ///< 网格名称，输出文件前缀
                  
    std::vector<Element> v_elem;                      ///< 构成网格的单元容器
    std::vector<Face>    v_face;                      ///< 构成网格的面容器
    std::vector<Node>    v_node;                      ///< 构成网格的坐标容器

    std::vector<std::string> v_boundaryName;          ///< 边界名称
    std::vector<std::vector<int>> vv_boundaryFaceID;  ///< 边界面编号

    MultiStructuredBlock multiStructuredBlock;        ///< 结构网格分块信息

public:    
#if defined(_BaseParallelMPI_)
public:
    /**
     * @brief 序列化函数(用于MPI并行)
     * @tparam Archive 序列化类型
     * @param ar 序列化对象
     * @param version 版本号
     */
    template<class Archive>
    void serialize(Archive & ar, const unsigned int version)
    {
        ar & zoneID;
        ar & st_fileName;
        ar & st_meshName;
        ar & n_elemNum;
        ar & n_elemNum_all;
        ar & n_faceNum;
        ar & n_nodeNum;
        ar & md_meshDim;

        ar & est_shapeType;
        ar & v_elem;
        ar & v_face;
        ar & v_node;

        ar & v_boundaryName;
        ar & vv_boundaryFaceID;
        /// 注意其他没有并行发送的数据，都是独自生成的
    }
#endif

    // 友元类声明
    friend class DecomposeManager;          ///< 网格分解管理器
    friend class AgglomerateManager;        ///< 网格聚合管理器
    friend class AgglomerateStructuredMesh; ///< 结构化网格聚合
    friend class DualMesh;                  ///< 对偶网格
    friend class MeshSorting;               ///< 网格排序
    friend class WallDistanceBase;          ///< 壁面距离计算基类
    friend class WallDistanceManager;       ///< 壁面距离管理器
    friend class MeshProcess;               ///< 网格处理
    friend class CgnsMesh;                  ///< CGNS网格
    friend class CgnsMeshStructured;        ///< 结构化CGNS网格
    friend class DlgMesh;                   ///< DLG网格
    friend class FluentMeshBlock;           ///< Fluent网格块
    friend class MeshConverter;             ///< 网格转换器
    friend class MeshConvertManager;        ///< 网格转换管理器
    friend class SubMesh;                   ///< 子网格
    friend class OversetMesh;               ///< 重叠网格
};

#endif // _basic_mesh_BaseMesh_
