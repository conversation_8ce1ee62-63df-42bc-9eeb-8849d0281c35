﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file OuterLoop.h
//! <AUTHOR>
//! @brief 流场解算器类.
//! @date 2022-03-17
//
//------------------------------修改日志----------------------------------------
// 2022-03-17 李艳亮、乔龙
//     说明：设计功能模块框架
//------------------------------------------------------------------------------

#ifndef _sourceFlow_flowSolver_OuterLoop_
#define _sourceFlow_flowSolver_OuterLoop_

#include "sourceFlow/flowSolver/FullMultigird.h"
#include "sourceFlow/flowSolver/InnerLoop.h"

#if defined(_EnableFlutter_)
#include "sourceFlow/flowSolver/StructuralDynamics.h"
#include "meshProcess/motion/MotionManager.h"
#endif

/**
 * @brief 流场计算外循环类
 * 
 */
class OuterLoop
{
public:
    /**
     * @brief流场解算器构造函数
     * 根据设置参数文件，建立基于当地网格的特定流体流动解算器
     * 
     * @param[in] subMesh_ 当地网格，含细网格和所有粗网格
     * @param[in] flowConfig_ 流场相关设置参数，含输入输出控制、离散格式、求解策略、边界参数等
     */
    OuterLoop(SubMesh *subMesh_, Configure::Flow::FlowConfigure &flowConfig_);

    /**
     * @brief 析构函数
     * 释放流场解算器构造函数中通过new建立的数据
     * 
     */
    ~OuterLoop();

    /**
     * @brief 流场解算器求解函数
     * 
     */
    void Solve();

    /**
     * @brief 流场初始化
     * 
     */
    void Initialize();

    void CloseMultigrid();

    const Package::FlowPackage *GetFlowPackage()const { return this->flowPackageVector[0];} 

private:

    /**
     * @brief 设置当前计算物理时间步长
     * 
     */
    void SetTimeStep();
    
    /**
     * @brief 保存上一物理时间步流场解变量
     * 
     * @param[in] outerStep 外循环迭代步数
     */
    void UpdateOldField(const int &outerStep);

private:
    /// 当地网格，含细网格和所有粗网格
    SubMesh *subMesh;

    /// 流场相关设置参数，含输入输出控制、离散格式、求解策略、边界参数等
    Configure::Flow::FlowConfigure &flowConfigure;

    /// 由细网格和所有粗网格上的物理场包所构成的容器, 容器大小为总网格层数
    std::vector<Package::FlowPackage *> flowPackageVector;

    /// 对于不同层级的网格，流场求解时采用的具体时间推进对象所构成的容器, 容器大小为总网格层数
    std::vector<Time::Flow::FlowTimeManager *> timeSchemeVector;

    /// 多重网格总层数
    int multigridLevel;

    /// 多重网格求解器对象
    FullMultigird *fullMultigird;

    /// 内循环求解器
    InnerLoop *innerLoop;

    /// 流场计算结果输出对象
    FlowResultsProcess *resultProcess;

    /// 初始化类型
    Initialization::Type initialType;

    /// 非定常标识,true为非定常，false为定常
    bool unsteady;

    /// 双时间步标识,true为双时间步，false为定常
    bool dualTime;

    /// 全多重标识
    bool fullMultigirdFlag;

#if defined(_EnableFlutter_)
    std::unique_ptr<StructuralDynamics> structuralDynamics;
    std::unique_ptr<MotionManager> motionManager;
    bool enableFlutter = false;
    double physicalTimeStep = 0.0;

    // 计算广义气动力（投影到模态上）
    std::vector<double> ComputeGeneralizedAerodynamicForces();

    // 更新网格度量（体积、面积、法向量等）
    void UpdateMeshMetrics();

    // 初始化颤振计算
    void InitializeFlutterCalculation();

    // 将广义位移转换为物理位移场
    void ConvertGeneralizedToPhysicalDisplacement(const std::vector<double>& generalizedDisp);

    // 读取摄动场数据
    void LoadPerturbationFields();
#endif
};

#endif
