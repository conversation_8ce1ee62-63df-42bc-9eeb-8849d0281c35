﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file MeshProcess.h
//! <AUTHOR>
//! @brief 网格管理类，用于管理各类网格转换与操作
//! @date 2021-04-13
//
//------------------------------修改日志----------------------------------------
// 2021-04-13 乔龙
//    说明：建立并规范化。
//------------------------------------------------------------------------------

#ifndef _meshProcess_meshProcess_MeshProcess_
#define _meshProcess_meshProcess_MeshProcess_

#include "basic/configure/Configure.h"
#include "meshProcess/agglomeration/AgglomerateManager.h"
#include "meshProcess/decompose/DecomposeManager.h"
#include "meshProcess/dualMesh/DualMesh.h"
#include "meshProcess/meshConverter/MeshConvertManager.h"
#include "meshProcess/meshProcess/MeshChecker.h"

/// 网格前处理类
class MeshProcess
{
public:
    /**
     * @brief 构造函数
     * 
     * @param[in] configure_ 参数对象
     */
    MeshProcess(Configure::Configure &configure_);

    /**
     * @brief 析构函数
     * 
     */
    ~MeshProcess();

    /**
     * @brief 前处理操作
     * 
     */
	void PreProcess(SubMesh *localMesh = nullptr, std::vector<Face> *wallFace_ = nullptr);

    /**
     * @brief 读取并分区摄动场
     * 
     */
    void ReadAndPartitionPerturbationField();

    /**
     * @brief 网格读取与转化
     * 
     */
    void GenerateMesh();

    /**
     * @brief 对偶网格处理
     * 
     */
    void ProcessDualMesh(); 

    /**
     * @brief 网格聚合
     * 
     */
    void AgglomerateMesh();

    /**
     * @brief 网格分区
     *
     */
    void ParallelPartition();
    
    /**
     * @brief 生成各子域网格分区数量
     *
     * @param[in] nPart 总分区数
     * @param[in] zonePartInfo 各子域网格分区数
     */
    void GenerateZonePartSize(const int nPart, std::vector<int> &zonePartInfo);	
    
private:
    Configure::Configure &configure; ///< 参数文件对象
    std::vector<SubMesh *> globalMeshVector; ///< 全局网格指针容器
	bool writeFileFlag; ///< 是否输出分区子网格到文件
	std::vector<SubMesh *> subMeshVector; ///< 分区网格指针容器
    std::vector<Face> *wallFace; ///< 壁面边界的所有面
	int mpiSize; ///< MPI进程数
	int mpiRank; ///< MPI进程号
};

#endif 