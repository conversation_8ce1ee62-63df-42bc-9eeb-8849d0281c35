﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file Configure.h
//! <AUTHOR>
//! @brief 读xml文件的类。包含用户交接界面与生成boundary.xml文件的函数。
//! @date 2021-03-30
//
//------------------------------修改日志----------------------------------------
// 2021-04-09 吴奥奇
//    说明：修改GenerateBoundaryFile函数
//            添加材料参数
//            添加获得湍流参数的俩函数
//            添加获得边界条件类型的函数
//
// 2021-04-07 吴奥奇
//    说明：由于xml文件内容改变，重写Read函数
//            
// 2021-03-30 吴奥奇
//    说明：代码规范化，减少pushback
//            添加命名空间
//------------------------------------------------------------------------------

#ifndef _basic_configure_Configure_
#define _basic_configure_Configure_

#include "basic/configure/ConfigureMacro.h"
#include "basic/configure/ConfigureMacro.hxx"
#include "basic/configure/Parameter.h"
#include "basic/configure/PropertyTree.h"
#include "basic/mesh/SubMesh.h"

/**
 * @brief 参数命名空间
 * 
 */
namespace Configure
{

struct CylinderStruct
{
    Scalar radius0;
    Scalar radius1;
    Vector axisP0;
    Vector axisP1;
    int wallNumber;
	CylinderStruct()
	{
		radius0 = Scalar0;
		radius1 = Scalar0;
		axisP0 = Vector0;
		axisP1 = Vector0;
		wallNumber = 0;
	}
};

struct PlaneStruct
{
    int nodeSize;
    std::vector<Vector> nodes;
	PlaneStruct()
	{
		nodeSize = 0;
	}
};

struct GeometryStruct
{
    int dimension;
    int numShape;
    std::vector<CylinderStruct> cylinders;
    std::vector<PlaneStruct> planes;

	GeometryStruct()
	{
		dimension = 2;
		numShape = 0;
	}
};

/**
 * @brief 网格变换参数结构体
 * 
 */
struct MeshTransformStruct
{
    bool scaleFlag; ///< 0为网格不需要缩放， 1为网格需要缩放
    bool transferFlag; ///< 0为网格不需要平移， 1为网格需要平移
    bool rotateFlag; ///< 0为网格不需要旋转， 1为网格需要旋转
    Vector scale; ///< 缩放因子
    Vector transfer; ///< 平移位移
    Vector rotate; ///< 三个轴的正方向旋转的角度
	MeshTransformStruct()
	{
		scaleFlag = false;
		transferFlag = false;
		rotateFlag = false;
		scale = Vector(1, 1, 1);
		transfer = Vector0;
		rotate = Vector0;
	}
};

/**
 * @brief 网格参考量参数结构体
 * 
 */
struct ReferenceStruct
{
    Scalar cRef; ///< 平均气动弦长
    Scalar bRef; ///< 展长
    Scalar SRef; ///< 面积
    Vector cmRef; ///<力矩参考点
	ReferenceStruct()
	{
		cRef = 1.0;
		bRef = 1.0;
		SRef = 1.0;
		cmRef = Vector0;
	}
};

/**
 * @brief 网格信息参数结构体
 * 
 */
struct MeshStruct
{
    int meshNumber;
    std::vector<std::string> meshPath; ///< 网格路径
	std::vector<std::string> fileName; ///< 网格文件名称
    std::vector<Preprocessor::MeshType> meshType; ///< 网格类型
    Mesh::MeshDim dimension; ///< 网格维度
    MeshTransformStruct meshTransform; ///< 网格变换参数结构体
    ReferenceStruct reference; ///< 网格参考量结构体
	MeshStruct()
	{
		meshNumber = 1;
		meshPath.clear();
		fileName.clear();
		meshType.clear();
		dimension = Mesh::MeshDim::md2D;
	}
};

/**
 * @brief 边界条件参数结构体
 * 
 */
struct BoundaryStruct
{
    int globalID; ///< 边界全局编号
    std::string name; ///< 边界名称
    Boundary::Type type; ///< 边界类型
    std::vector<std::string> value; ///< 边界值列表
    int zoneID; ///< 边界所属网格编号
	BoundaryStruct()
	{
		globalID = 0;
		name = "";
		type = Boundary::Type::WALL_ADIABATIC;
        zoneID = 0;
	}
};

/**
 * @brief 网格聚合参数结构体
 * 
 */
struct MultigridStruct
{
    int totalLevel; ///< 总网格层数
    Preprocessor::AgglomerateType type; ///< 多重网格聚合方法
    int minCoarseRation; ///< 最小聚合率
    int maxCoarseRation; ///< 最大聚合率
    int boundaryLayerNumber; ///< 边界层网格法向聚合总层数
    int boundaryLayerCoarseRationNormal; ///< 边界层法向聚合率
    int boundaryLayerCoarseRationTangent; ///< 边界层切向聚合率
	bool singletonsRemovementFlag; ///< 去除孤立单元标志
	MultigridStruct()
	{
		totalLevel = 5;
		type = Preprocessor::AgglomerateType::SEED;
		minCoarseRation = 0;
		maxCoarseRation = 0;
		boundaryLayerNumber = 40;
		boundaryLayerCoarseRationNormal = 2;
		boundaryLayerCoarseRationTangent = 2;
		singletonsRemovementFlag = true;
	}
};

/**
 * @brief 前处理参数结构体
 * 
 */
struct PreprocessStruct
{
    int threadNumber; ///< 进程数
    int partitionNumber; ///< 分区数量
    Preprocessor::DecomposeType partitionMethod; ///< 分区方法
    bool dualMeshFlag; ///< 对偶标识
    MultigridStruct multigrid; ///< 网格聚合参数结构体
    Turbulence::WallDistance wallDistanceMethod; ///< 壁面距离计算方法
    Preprocessor::RenumberType renumberMethod; ///< 网格单元编号重排方法
    std::string outputPath; ///< 前处理结果文件输出路径
    bool binaryFileFlag; ///< 前处理文件二进制输出标识
	PreprocessStruct()
	{
		threadNumber = 1;
		partitionNumber = 1;
		partitionMethod = Preprocessor::DecomposeType::NONE_DECOMPOSE;
		dualMeshFlag = true;
		wallDistanceMethod = Turbulence::WallDistance::NONE_WALL_DISTANCE;
		renumberMethod = Preprocessor::RenumberType::NONE_RENUMBER;
		outputPath = "./preMesh/";
		binaryFileFlag = true;
	}
};

/**
 * @brief 颤振扰动场结构体
 * 
 */
struct PerturbationStruct
{
    int perturbationNumber; ///< 摄动场数量
    std::string perturbationPath; ///< 摄动场路径
    std::string perturbationName; ///< 摄动场名称
    bool binaryFileFlag; ///< 摄动场二进制文件标识
    bool FlutterFlag;
	PerturbationStruct()
	{
		perturbationNumber = 0;
		perturbationPath = "./mesh/";
		perturbationName = "";
		binaryFileFlag = true;
        FlutterFlag = false;
	}
};


/**
 * @brief 控制参数类
 * 
 */
class Configure
{
public:
    /**
     * @brief 构造函数
     * 
     */
    Configure();

public:
    /**
     * @brief 读取网格及前处理参数
     * 
     * @param[in] fileName case文件名
     * @param[in] readBoundaryFlag 读取边界参数标识
     * 
     */
    void ReadCaseXml(const std::string &fileName, const bool readBoundaryFlag = true);
    
    /**
     * @brief 根据子网格参数更新子网格边界条件
     * 
     * @param[in] localMesh 子网格
     */
    void UpdateLocalBoundary(SubMesh *localMesh);

    /**
     * @brief 设置OpenMP线程数
     * 
     */
    void SetOpenMPThread(const int threadNum = -1);
    
    /**
     * @brief 获取工程名称
     * 
     * @return const std::string&
     */
    const std::string &GetCaseName() const { return caseName; }

    /**
     * @brief 设置工程名称
     * 
     * @return const std::string&
     */
    void SetCaseName(const std::string &caseName_) { caseName = caseName_; }

    /**
    * @brief 设置工作路径
    *
    * @param[in] path 工作路径
    */
    void SetWorkPath(const std::string &path){ workPath = path; }

    /**
     * @brief 获取工作路径
     * 
     * @return const std::string&
     */
    const std::string &GetWorkPath() const { return workPath; }

    /**
     * @brief 设置工程名称
     * 
     * @return const std::string&
     */
    void SetPartitionNumber(const int &number) { this->preprocess.partitionNumber = number; }

    /**
     * @brief 设置前处理结果保存路径
     * 
     * @return const std::string&
     */
    void SetPreprocessOutputPath(const std::string &path_) { preprocess.outputPath = path_; }

    /**
     * @brief 获取几何信息参数
     * 
     * @return const GeometryStruct&
     */
    const GeometryStruct &GetGeometryParameters() const { return geometryStruct; }

    /**
     * @brief 获取网格信息参数
     * 
     * @return const MeshStruct&
     */
    const MeshStruct &GetMeshParameters() const { return meshStruct; }

    /**
     * @brief 获取前处理参数
     * 
     * @return const PreprocessStruct&
     */
    const PreprocessStruct &GetPreprocess() const { return preprocess; }

    /**
     * @brief 获取摄动场处理参数
     * 
     * @return const PreprocessStruct&
     */
    const PerturbationStruct &GetPerturbation() const { return perturbationStruct; }

    /**
     * @brief 获取当地边界参数
     * 
     * @param[in] level 当地网格层级
     * @param[in] patchID 边界条件编号
     * @return const BoundaryStruct&
     */
    const BoundaryStruct &GetLocalBoundary(const int &level, const int &patchID)const {return localBoundary[level][patchID];}

    /**
     * @brief 获取全局边界名称列表
     * 
     * @return const std::vector<std::string>&
     */
    const std::vector<std::string> &GetGlobalBoundaryNameList()const {return globalBoundaryName;}

	/**
	* @brief 获取全局边界数量
	*
	* @return const int&
	*/
	const int &GetGlobalBoundarySize()const { return globalBoundaryNumber; }

	/**
	* @brief 获取全局边界信息
	*
    * @param[in] patchID 边界条件编号
	* @return const BoundaryStruct&
	*/
	const BoundaryStruct &GetGlobalBoundary(const int &patchID)const { return globalBoundary[patchID]; }

    /**
     * @brief 判断当地边界是否为壁面
     * 
     * @param[in] level 当地网格层级
     * @param[in] patchID 当地边界编号
     * @return true 是壁面
     * @return false 不是壁面
     */
    bool JudgeWallLocal(const int &level, const int &patchID)const {return localBoundary[level][patchID].type > Boundary::Type::WALL;}

    /**
     * @brief 判断全局边界是否为壁面
     * 
     * @param patchID 全局边界编号
     * @return true 是壁面
     * @return false 不是壁面
     */
    bool JudgeWallGlobal(const int &patchID)const {return globalBoundary[patchID].type > Boundary::Type::WALL;}

    /**
     * @brief 判断当地边界是否为对称面
     * 
     * @param[in] level 当地网格层级
     * @param patchID 当地边界编号
     * @return true 是对称面
     * @return false 不是对称面
     */
    bool JudgeSymmetryLocal(const int &level, const int &patchID)const {return localBoundary[level][patchID].type == Boundary::Type::SYMMETRY;}

    /**
	* @brief 判断是否启动重叠网格模块
	*
	*/
	const bool &JudgeEnableOversetMesh()const { return this->enableOversetMesh; }

    /**
	* @brief 判断全局边界是否为重叠边界
	*
	* @param patchID 全局边界编号
	* @return true 是重叠边界
	* @return false 不是重叠边界
	*/
	bool JudgeOversetGlobal(const int &patchID)const { return globalBoundary[patchID].type == Boundary::Type::OVERSET;}

    /**
	* @brief 判断是否启动运动模块
	*
	*/
	const bool &JudgeEnableMotion()const { return this->enableMotion; }

    /**
     * @brief 打印参数
     * 
     */
    void PrintInformation();

	/**
	* @brief 输出XML文件
	*
    * @param fileName_ 文件名称
	*/
	void WriteFile(const std::string fileName_ = "");

    /**
     * @brief 获取短舱入口边界对应全局编号
     * 
     */
	const std::vector<int> &GetNacelleInletGlobalIdVector()const {return this->nacelleInletGlobalIdVector;}

	/**
	* @brief 获取短舱出口边界对应全局编号
	*
	*/
	const std::vector<int> &GetNacelleOutletGlobalIdVector()const { return this->nacelleOutletGlobalIdVector; }

    /**
     * @brief 获取质量流量入口边界对应全局编号
     * 
     */
	const std::vector<int> &GetMassFlowInletGlobalIdVector()const {return this->massFlowInletGlobalIdVector;}
    
    /**
     * @brief 获取质量流量出口边界对应全局编号
     * 
     */
	const std::vector<int> &GetMassFlowOutletGlobalIdVector()const {return this->massFlowOutletGlobalIdVector;}
    
protected:

    /**
     * @brief 读取网格及前处理参数
     * 
     * @param[in] fileName case文件名
     * @param[in] readBoundaryFlag 读取边界参数标识
     * 
     */
    void ReadBasicCaseXml(const std::string &fileName, const bool readBoundaryFlag = true);

    /**
     * @brief 提取网格路径
     * 
     * @param stringTemp 输入的网格名称字符串
     * @return std::string
     */
    std::string ObtainMeshPath(std::string stringTemp);

    /**
     * @brief 提取网格名称（不含路径及后缀）
     * 
     * @param stringTemp 输入的网格名称字符串
     * @return std::string 
     */
    std::string ObtainMeshName(std::string stringTemp);

	/**
	* @brief 提取文件名称（不含路径）
	*
	* @param stringTemp 输入的网格名称字符串
	* @return std::string
	*/
	std::string ObtainFileName(std::string stringTemp);

    /**
     * @brief 提取网格文件类型
     * 
     * @param stringTemp 输入的网格名称字符串
     * @return Preprocessor::MeshType 
     */
    Preprocessor::MeshType ObtainMeshType(std::string stringTemp);

	/**
	* @brief 生成绝对路径
	*
	* @param stringTemp 输入字符串
	* @return std::string
	*/
	std::string ObtainAbsolutePath(std::string stringTemp);
	
    /**
     * @brief 检查参数是否合理
     * 
     */
	void InspectConfigure();
	
private:

	/**
	* @brief 读取工程名称
	*
	* @param ptree
	*/
	void ReadCaseName(PropertyTree &ptree);

	/**
	* @brief 读取几何信息参数
	*
	* @param ptree
	*/
	void ReadGeometry(PropertyTree &ptree);

	/**
	* @brief 读取网格信息参数
	*
	* @param ptree
	*/
	void ReadMesh(PropertyTree &ptree);

    /**
    * @brief 读取边界信息参数
    *
    * @param ptree
    */
    void ReadBoundaryCondition(PropertyTree &ptree);

	/**
	* @brief 读取前处理参数
	*
	* @param ptree
	*/
	void ReadPreprocess(PropertyTree &ptree);

protected:
    std::string caseName; ///< 工程名称
    std::string workPath; ///< 工作路径

    GeometryStruct geometryStruct; ///< 几何参数结构体
    MeshStruct meshStruct; ///< 网格参数结构体
    PreprocessStruct preprocess; ///< 前处理参数结构体
    PerturbationStruct perturbationStruct; ///< 摄动场参数结构体
    
    int globalBoundaryNumber; ///< 全局边界条件数量
    bool enableOversetMesh; ///<重叠模块启动标识
    bool enableMotion; ///< 运动模块启动标识
    std::string boundaryFilePath; ///< 边界条件文件
    std::vector<BoundaryStruct> globalBoundary; ///< 全局边界容器
    std::vector<std::string> globalBoundaryName; ///< 全局边界名称容器
    std::vector<Boundary::Type> globalBoundaryType; ///< 全局边界类型容器
    std::vector<std::vector<BoundaryStruct>> localBoundary; ///< 当地边界容器

    std::vector<int> massFlowInletGlobalIdVector; ///<  质量流量入口边界所对应的全局编号
    std::vector<int> massFlowOutletGlobalIdVector; ///< 质量流量出口边界所对应的全局编号
    std::vector<int> nacelleInletGlobalIdVector; ///< 短舱入口边界所对应的全局编号
	std::vector<int> nacelleOutletGlobalIdVector; ///< 短舱出口边界所对应的全局编号
    
    std::ostringstream outString; ///< 用于一次性输出多个字符串
	bool fullOutputFlag;
    
    std::vector<std::pair<std::string, Parameter>> mapPosValue;
};
} // namespace Configure
#endif