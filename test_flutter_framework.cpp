/**
 * @brief 颤振框架测试程序
 * 用于验证广义坐标系统的基本功能
 */

#include "sourceFlow/flowSolver/StructuralDynamics.h"
#include "basic/common/CommonInclude.h"
#include <iostream>
#include <vector>

void TestStructuralDynamics()
{
    std::cout << "=== 测试结构动力学类 ===" << std::endl;
    
    // 创建结构动力学对象
    StructuralDynamics structDyn;
    
    // 测试初始化（需要模态数据文件）
    try {
        structDyn.Initialize("modal_data_example.dat");
        std::cout << "✓ 结构动力学初始化成功" << std::endl;
        std::cout << "模态数量: " << structDyn.GetNumberOfModes() << std::endl;
    }
    catch (const std::exception& e) {
        std::cout << "✗ 初始化失败: " << e.what() << std::endl;
        return;
    }
    
    // 测试设置阻尼参数
    std::vector<double> dampingRatios = {0.02, 0.03, 0.025}; // 2%, 3%, 2.5%
    structDyn.SetDampingRatio(dampingRatios);
    std::cout << "✓ 阻尼参数设置成功" << std::endl;
    
    // 测试Newmark参数设置
    structDyn.SetNewmarkParameters(0.25, 0.5);
    std::cout << "✓ Newmark参数设置成功" << std::endl;
    
    // 测试广义力求解
    std::vector<double> generalizedForces = {100.0, 50.0, 25.0}; // 示例广义力
    double timeStep = 0.001; // 1ms时间步
    
    std::cout << "\n=== 时间积分测试 ===" << std::endl;
    for (int step = 0; step < 5; step++) {
        structDyn.SolveGeneralizedEquation(generalizedForces, timeStep);
        
        const auto& disp = structDyn.GetGeneralizedDisplacement();
        const auto& vel = structDyn.GetGeneralizedVelocity();
        const auto& acc = structDyn.GetGeneralizedAcceleration();
        
        std::cout << "步骤 " << step + 1 << ":" << std::endl;
        std::cout << "  广义位移: ";
        for (double d : disp) std::cout << d << " ";
        std::cout << std::endl;
        std::cout << "  广义速度: ";
        for (double v : vel) std::cout << v << " ";
        std::cout << std::endl;
        std::cout << "  广义加速度: ";
        for (double a : acc) std::cout << a << " ";
        std::cout << std::endl << std::endl;
    }
    
    // 测试模态振型插值
    std::cout << "=== 模态振型测试 ===" << std::endl;
    Vector testPoint(1.5, 0.0, 0.0); // 测试点
    for (int mode = 0; mode < structDyn.GetNumberOfModes(); mode++) {
        Vector modeShape = structDyn.GetModeShapeAtPoint(mode, testPoint);
        std::cout << "模态 " << mode + 1 << " 在点 (1.5,0,0) 的振型: " 
                  << modeShape.GetX() << ", " << modeShape.GetY() << ", " << modeShape.GetZ() << std::endl;
    }
    
    std::cout << "\n✓ 所有测试完成" << std::endl;
}

void TestModalDataFormat()
{
    std::cout << "\n=== 模态数据格式验证 ===" << std::endl;
    
    std::ifstream file("modal_data_example.dat");
    if (!file.is_open()) {
        std::cout << "✗ 无法打开模态数据文件" << std::endl;
        return;
    }
    
    std::string line;
    int numModes;
    
    // 读取模态数量
    if (std::getline(file, line)) {
        std::istringstream iss(line);
        iss >> numModes;
        std::cout << "读取到模态数量: " << numModes << std::endl;
    }
    
    // 验证每个模态的数据
    for (int mode = 0; mode < numModes; mode++) {
        double frequency;
        int numNodes;
        
        // 读取频率
        if (std::getline(file, line)) {
            std::istringstream iss(line);
            iss >> frequency;
            std::cout << "模态 " << mode + 1 << " 频率: " << frequency << " rad/s" << std::endl;
        }
        
        // 读取节点数
        if (std::getline(file, line)) {
            std::istringstream iss(line);
            iss >> numNodes;
            std::cout << "  节点数量: " << numNodes << std::endl;
        }
        
        // 验证节点数据格式
        for (int node = 0; node < numNodes; node++) {
            if (std::getline(file, line)) {
                std::istringstream iss(line);
                double x, y, z, dx, dy, dz;
                if (iss >> x >> y >> z >> dx >> dy >> dz) {
                    if (node == 0) { // 只显示第一个节点的数据
                        std::cout << "  首节点: 坐标(" << x << "," << y << "," << z 
                                  << ") 振型(" << dx << "," << dy << "," << dz << ")" << std::endl;
                    }
                } else {
                    std::cout << "✗ 节点数据格式错误" << std::endl;
                }
            }
        }
    }
    
    file.close();
    std::cout << "✓ 模态数据格式验证完成" << std::endl;
}

int main()
{
    std::cout << "颤振框架测试程序" << std::endl;
    std::cout << "==================" << std::endl;
    
    // 测试模态数据格式
    TestModalDataFormat();
    
    // 测试结构动力学类
    TestStructuralDynamics();
    
    std::cout << "\n测试程序结束" << std::endl;
    return 0;
}
