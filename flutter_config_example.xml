<?xml version="1.0" encoding="UTF-8"?>
<!-- 颤振计算配置示例 -->
<Configuration>
    <!-- 其他配置项... -->
    
    <!-- 颤振计算参数 -->
    <Flutter>
        <!-- 是否启用颤振计算 -->
        <EnableFlutter>true</EnableFlutter>
        
        <!-- 是否使用强耦合（true=强耦合，false=弱耦合） -->
        <StrongCoupling>false</StrongCoupling>
        
        <!-- 强耦合时的最大FSI迭代次数 -->
        <MaxFSIIterations>10</MaxFSIIterations>
        
        <!-- FSI收敛容差 -->
        <FSITolerance>1e-6</FSITolerance>
        
        <!-- 各模态的固有频率 (rad/s) -->
        <NaturalFrequencies>
            <Mode1>15.7</Mode1>  <!-- 第1阶弯曲模态：2.5 Hz -->
            <Mode2>62.8</Mode2>  <!-- 第2阶扭转模态：10 Hz -->
            <Mode3>157.1</Mode3> <!-- 第3阶弯曲模态：25 Hz -->
        </NaturalFrequencies>
        
        <!-- 各模态的阻尼比 -->
        <DampingRatios>
            <Mode1>0.02</Mode1>  <!-- 2% 阻尼 -->
            <Mode2>0.03</Mode2>  <!-- 3% 阻尼 -->
            <Mode3>0.025</Mode3> <!-- 2.5% 阻尼 -->
        </DampingRatios>
        
        <!-- Newmark-β时间积分参数 -->
        <NewmarkBeta>0.25</NewmarkBeta>    <!-- 平均加速度法 -->
        <NewmarkGamma>0.5</NewmarkGamma>   <!-- 平均加速度法 -->
    </Flutter>
    
    <!-- 摄动场配置（现有配置） -->
    <Perturbation>
        <PerturbationNumber>3</PerturbationNumber>
        <PerturbationPath>./perturbation/</PerturbationPath>
        <PerturbationName>mode</PerturbationName>
        <BinaryFileFlag>true</BinaryFileFlag>
        <FlutterFlag>true</FlutterFlag>
    </Perturbation>
    
    <!-- 时间推进配置 -->
    <TimeScheme>
        <OuterLoopType>UNSTEADY</OuterLoopType>
        <PhysicalTimeStep>0.001</PhysicalTimeStep>  <!-- 1ms时间步 -->
        <MaxTimeSteps>10000</MaxTimeSteps>
        <!-- 其他时间推进参数... -->
    </TimeScheme>
    
    <!-- 其他配置项... -->
</Configuration>

<!-- 
配置说明：
1. EnableFlutter: 启用颤振计算
2. StrongCoupling: 选择耦合方式
   - true: 强耦合，每个时间步内进行FSI迭代
   - false: 弱耦合，每个时间步只计算一次
3. NaturalFrequencies: 必须与摄动场数量匹配
4. DampingRatios: 可选，默认为2%
5. 摄动场文件必须正确加载，FlutterFlag设为true

使用步骤：
1. 准备摄动场文件（mode_1.dat, mode_2.dat, mode_3.dat等）
2. 设置正确的固有频率
3. 启用颤振计算
4. 运行求解器
-->
