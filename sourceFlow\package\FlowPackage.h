﻿////////////////////////////////////////////////////////////////////////////////////
////---------------------------------------ARI-CFD-----------------------------/////
////------------------------中国航空工业空气动力研究院-------------------------/////
///////////////////////////////////////////////////////////////////////////////////
//! @file      FlowPackage.h
//! @brief     流场的基本数据和相关操作包
//! <AUTHOR>
//! @date      2021-08-10
//
//------------------------------修改日志----------------------------------------
//
//  2021-08-10 李艳亮、乔龙
//    说明：创建并进行规范化。
//------------------------------------------------------------------------------

# ifndef _sourceFlow_package_FlowPackage_
# define _sourceFlow_package_FlowPackage_

#include "basic/CFD/linearSystemSolver/BlockSparseMatrixPetsc.h"
#include "basic/CFD/linearSystemSolver/BlockSparseMatrixSelf.h"
#include "basic/field/DataPackage.h"
#include "basic/field/FieldManipulation.h"
#include "sourceFlow/material/Materials.h"
#include "sourceFlow/configure/FlowConfigure.h"
#include "meshProcess/zone/ZoneManager.h"
#include "basic/MRF/MRFModel.h"

/**
 * @brief 场包命名空间
 * 
 */
namespace Package
{
/**
 * @brief 当前网格信息结构体
 * 
 */
struct MeshStruct
{
    Mesh *mesh; ///< 网格指针
    bool dim2; ///< 流动是否是二维
    bool dim3; ///< 流动是否是三维
    int level; ///< 当前网格位于多重网格的第几层
    int processorID; ///< 当前网格归属的进程编号
};

/**
 * @brief 场指针结构体
 * 
 */
struct FieldPointer
{
    ElementField<Scalar> *density; ///< 密度
    ElementField<Vector> *velocity; ///< 速度
    ElementField<Scalar> *pressure; ///< 压强
    ElementField<Scalar> *temperature; ///< 温度
    ElementField<Scalar> *enthalpy; ///< 总焓

    ElementField<Scalar> *soundSpeed; ///< 当地声速

    ElementField<Scalar> *muLaminar; ///< 层流粘性系数
    ElementField<Scalar> *muTurbulent; ///< 湍流粘性系数
    ElementField<Scalar> *alphaT; ///< 有效湍流粘性系数，仅用于k-v2-omega模型
    ElementField<Scalar> *muTurbulentRANS; ///< 湍流方程中的湍流粘性系数，仅用于机器学习

    ElementField<Scalar> *lambdaConvective; ///< 对流项的谱半径
    ElementField<Scalar> *lambdaViscous; ///< 粘性项的谱半径

    std::vector<ElementField<Scalar> *> turbulence; ///< 待求湍流量的指针容器

#if defined(_EnableMultiSpecies_)
	std::vector<ElementField<Scalar> *> massFraction; ///< 多组分质量分数的指针容器
#endif

#if defined(_EnableMLTurbModel_)
    std::vector<ElementField<Scalar> *> features; ///< 机器学习模型输入特征指针容器
#endif

    ElementField<Scalar> *deltaT; ///< 时间步长
    ElementField<Scalar> *pressureSwitch; ///< 压力开关（用于间断面的处理）

    std::vector<ElementField<Scalar>*> jacobianTurbulence; ///< 当采用RK时：湍流的点隐式谱半径，即delta(ResTur)/delta(Tur)
                                                           ///< 当采用LU时：湍流的对角阵D中源项贡献部分

    ElementField<int> *oversetElemType; ///<重叠网格单元类型场，主要用于后处理输出

	ElementField<Vector> *meshVelocity; ///<网格运动速度场

	ElementField<Scalar> *ldOverlr; ///< l_des与l_rans的比
	ElementField<Scalar> *shieldingFunction; ///< DDES的延迟函数


	ElementField<Scalar> *meanMuTurbulent; ///< 时均湍流黏性系数场
	ElementField<Scalar> *meanMu; ///< 时均主流黏性系数场
	ElementField<Scalar> *meanPressure; ///< 时均压力场
	ElementField<Vector> *meanVelocity; ///< 时均速度场
	ElementField<Scalar> *meanDensity; ///< 时均密度场

#if defined(_EnableFlutter_)
	std::vector<ElementField<Vector>*> perturbationFields; ///< 全场摄动场（模态振型）
	std::vector<std::vector<Vector>> wallPerturbationFields; ///< 物面摄动场（从全场中提取）
	std::vector<int> wallNodeMapping; ///< 物面节点ID映射
#endif
};

/**
 * @brief 上一步或上上步的流场量指针结构体
 * 
 */
struct FieldOldPointer
{
    ElementField<Scalar> *density; ///< 密度
    ElementField<Vector> *velocity; ///< 速度
    ElementField<Scalar> *pressure; ///< 压强
    std::vector<ElementField<Scalar> *> turbulence; ///< 湍流量指针容器
#if defined(_EnableMultiSpecies_)
	std::vector<ElementField<Scalar> *> massFraction; ///< 多组分指针容器
#endif
};

/**
 * @brief 梯度指针结构体
 * 
 */
struct GradientFieldPointer
{
    ElementField<Vector> *gradientRho; ///< 密度的梯度
    ElementField<Tensor> *gradientU; ///< 速度的梯度
    ElementField<Vector> *gradientP; ///< 压强的梯度
    ElementField<Vector> *gradientT; ///< 温度的梯度
    std::vector<ElementField<Vector> *> gradientTurbulence; ///< 湍流量的梯度
#if defined(_EnableMultiSpecies_)
	std::vector<ElementField<Vector> *> gradientMassFraction; ///< 组分的梯度
#endif
};

/**
 * @brief 残差指针结构体
 * 
 */
struct ResidualFieldPointer
{
    ElementField<Scalar> *residualMass; ///< 质量方程的残差
    ElementField<Vector> *residualMomentum; ///< 动量方程的残差
    ElementField<Scalar> *residualEnergy; ///< 能量方程的残差
    std::vector<ElementField<Scalar> *> residualTurbulence; ///< 湍流量的残差
#if defined(_EnableMultiSpecies_)
	std::vector<ElementField<Scalar> *> residualMassFraction; ///< 多组分的残差
#endif
};

/**
 * @brief 隐式求解结构体
 * 
 */
struct ImplicitSolver
{
	BlockSparseMatrix *jacobian; ///< 主流Jacobian矩阵
	BlockSparseMatrix *jacobianTur; ///< 湍流Jacobian矩阵
    bool implicitFlag; ///< 隐式计算标识
    bool updateJacobian; ///< 更新Jacobian矩阵标识
};

/**
 * @brief 材料属性参数结构体
 * 
 */
struct MaterialNumber
{
    Scalar Cp; ///< 等压比热容
    Scalar Cv; ///< 等容比
    Scalar R; ///< 气体常数
    Scalar gamma; ///< 比热比\gamma
    Scalar gamma1; ///< \gamma - 1.0    
    Scalar stokes; ///< 斯托克斯常数
    Scalar PrandtlLaminar; ///< 层流普朗特数
    Scalar PrandtlTurbulent; ///< 湍流普朗特数
};

/**
 * @brief 湍流信息结构体
 * 
 */
struct TurbulentStatus
{
    bool viscousFlag; ///< 流动是否是粘性
    bool laminarFlag; ///< 流动是否是层流
    bool turbulenceFlag; ///< 湍流标识：湍流为true, 其它为false
	bool desFlag; ///< DES标识：使用DES时为true, 其它为false
    int nVariable; ///< 湍流量（方程）的数量
    Turbulence::Model turbulenceType; ///< 湍流模型类型
    std::vector<FlowMacro::Scalar> variableMacro; ///< 湍流量的宏定义容器
    std::vector<FlowMacro::Scalar> variableMacro0; ///< 上一步湍流量的宏定义容器
    std::vector<FlowMacro::Scalar> variableMacro00; ///< 上上步湍流量的宏定义容器
    std::vector<FlowMacro::Scalar> residualMacro; ///< 湍流量残差的宏定义容器
    std::vector<FlowMacro::Vector> gradientMacro; ///< 湍流量梯度的宏定义容器
    std::vector<Scalar> *freeStreamValue; ///< 来流湍流量容器
};

/**
 * @brief 非定常参数结构体
 * 
 */
struct UnsteadyStatus
{
    bool unsteadyFlag; ///< 非定常标识：true为非定常
    bool dualTime; ///< 双时间步标识，true为双时间步
    Time::UnsteadyOrder unsteadyOrder; ///< 非定常阶数
    Scalar totalTime; ///< 总计算物理时间
    Scalar timeStep; ///< 物理时间步长
    Scalar currentTime; ///< 当前物理时间
	Scalar startTime; ///< 续算起始时间
};

/**
* @brief 流场计算需要的辅助信息
*
*/
struct ExtraInfo
{
    std::vector<Scalar> distance; ///< 当地网格体心连线距离
	std::vector<Vector> distanceNormal; ///< 体心连线的单位化（owner指向neigher）
};

#if defined(_EnableMultiSpecies_)
/**
* @brief 多组分信息结构体
*
*/
struct MultiSpeciesStatus
{
	int speciesSize;
	int reactionSize;
	std::vector<std::string> speciesList; ///< 组分名称列表
	std::vector<Scalar> massFractionR; ///< 来流组分质量分数
	std::vector<Scalar> massFractionP; ///< 来流组分质量分数

	//PureSpeciesStruct *pureSpeciesDataList;
	//std::vector<Scalar> molarMass; ///< 组分摩尔质量
	//std::vector<Scalar> gasConstant;
	//std::vector<Scalar> wellDepth;
	//std::vector<Scalar> diameter;
	//std::vector<Scalar> polarizability;
	//std::vector<Scalar> rotationalRelaxation;

	//std::vector<std::vector<Scalar>> nasaPolynomialLow; ///<  T<1000K, NASA多项式系数用于计算Cp、熵、焓
	//std::vector<std::vector<Scalar>> nasaPolynomialHigh; ///< T>1000K, NASA多项式系数用于计算Cp、熵、焓
};

struct PureSpeciesStruct //储存单个组分信息
{
	std::string speciesName;
	Scalar molarMass;                       ///<  摩尔质量
	Scalar gasConstant;
	std::vector<Scalar> nasaPolynomialLow;  ///<  T<1000K, NASA多项式系数用于计算Cp、熵、焓
	std::vector<Scalar> nasaPolynomialHigh; ///<  T>1000K, NASA多项式系数用于计算Cp、熵、焓
	Scalar wellDepth;
	Scalar diameter;
	Scalar polarizability;
	Scalar rotationalRelaxation;
};
#endif

/**
 * @brief 流场的基本数据和相关操作包
 * 
 */
class FlowPackage : public DataPackage
{
public:
    /**
     * @brief 构造函数，创建流场包对象
     * 
     * @param[in] level 网格层级
     * @param[in] subMesh 当前进程网格
     * @param[in] flowConfigure_ 控制参数
     */
    FlowPackage(const int &level, SubMesh *subMesh, Configure::Flow::FlowConfigure &flowConfigure_);

    /**
     * @brief 析构函数，释放流场包中通过new创建的所有对象
     * 
     */
    ~FlowPackage();

public:
    /**
     * @brief 获取网格信息结构体
     * 
     * @return const MeshStruct& 
     */
    const MeshStruct &GetMeshStruct()const { return meshStruct; }

    /**
     * @brief 获取材料对象
     * 
     * @return const MeshStruct& 
     */
    const Material::Flow::Materials &GetMaterial()const { return material; }

    /**
     * @brief 获取场指针结构体
     * 
     * @return const FieldPointer& 
     */
    const FieldPointer &GetField()const { return fieldPointer; }

    /**
     * @brief 获取上一时间步基本物理场指针结构体
     * 
     * @return const FieldOldPointer& 
     */
    const FieldOldPointer &GetField0()const { return field0Pointer; }
    
    /**
     * @brief 多重网格计算时，获取获取粗网格初始基本物理场指针结构体
     * 
     * @return const FieldOldPointer& 
     */
    const FieldOldPointer &GetField0Backup()const { return field0PointerBackup; }

    /**
     * @brief 非定常计算时，获取上一物理时间步基本物理场结构体
     * 
     * @return const std::vector<FieldOldPointer>& 
     */
    const std::vector<FieldOldPointer> &GetFieldUnsteadyField()const { return unsteadyFieldPointer; }

    /**
     * @brief 获取基本物理量梯度场结构体
     * 
     * @return const GradientFieldPointer& 
     */
    const GradientFieldPointer &GetGradientField()const { return gradientFieldPointer; }

    /**
     * @brief 获取基本物理场残值结构体
     * 
     * @return const ResidualFieldPointer& 
     */
    const ResidualFieldPointer &GetResidualField()const { return residualFieldPointer; }
    
    /**
     * @brief 获取基本物理场残值结构体
     * 
     * @return const ResidualFieldPointer& 
     */
    const ImplicitSolver &GetImplicitSolver()const { return implicitSolver; }
    
    /**
     * @brief 获取材料属性参数结构体
     * 
     * @return const MaterialNumber& 
     */
    const MaterialNumber &GetMaterialNumber()const { return materialNumber; }

    /**
     * @brief 获取湍流状态参数结构体
     * 
     * @return const TurbulentStatus& 
     */
    const TurbulentStatus &GetTurbulentStatus()const { return turbulentStatus; }

#if defined(_EnableMultiSpecies_)
    /**
     * @brief 获取多组分状态参数结构体
     * 
     * @return const MultiSpeciesStatus& 
     */
	const MultiSpeciesStatus &GetMultiSpeciesStatus() const { return multiSpeciesStatus; }
#endif

    /**
     * @brief 获取流场参数对象
     * 
     * @return Configure::Flow::FlowConfigure& 
     */
    Configure::Flow::FlowConfigure &GetFlowConfigure()const {return flowConfigure;}

    /**
     * @brief 获取非定常状态参数结构体
     * 
     * @return const UnsteadyStatus& 
     */
    const UnsteadyStatus &GetUnsteadyStatus()const {return unsteadyStatus;}

    /**
    * @brief 获取流场计算需要的辅助信息结构体
    *
    * @return const ExtraInfo&
    */
    const ExtraInfo &GetExtraInfo()const { return extraInfo; }    

    /**
    * @brief 获取当前网格边界类型
    *
    * @param[in] patchID 边界编号
    *
    * @return const Boundary::Type&
    */
    const Boundary::Type &GetLocalBoundaryType(const int &patchID)const { return this->flowBoundaryType[patchID]; }

    /**
	* @brief 获取多域管理器
	*
	* @return const ZoneManager*
	*/
	inline ZoneManager *GetZoneManager() { return zoneManager; }

    /**
     * @brief 获取MRF参数
     * 
     * @return const MRFStruct& 
     */
    inline const std::shared_ptr<MRFZONE>& GetMRF()const { return mrf; }

public:
    /**
     * @brief 获得指定单元的守恒量（当前步）
     * 
     * @param[in] elementID 单元编号
     * @param[out] rho 单元密度值
     * @param[out] rhoU 单元动量值
     * @param[out] rhoE 单元能量值
     */
    void GetConservedValue(const int &elementID, Scalar &rho, Vector &rhoU, Scalar &rhoE);

    /**
     * @brief 获得指定单元的守恒量（上一步）
     * 
     * @param[in] elementID 单元编号
     * @param[out] rho 单元密度值
     * @param[out] rhoU 单元动量值
     * @param[out] rhoE 单元能量值
     */
    void GetConservedValue0(const int &elementID, Scalar &rho, Vector &rhoU, Scalar &rhoE);

    /**
     * @brief 用给定守恒量计算原始量并存储在流场包中（当前步）
     * 
     * @param[in] elementID 单元编号
     * @param[in] rho 单元密度值
     * @param[in] rhoU 单元动量值
     * @param[in] rhoE 单元能量值
     */
    void SetPrimitiveValue(const int &elementID, const Scalar &rho, const Vector &rhoU, const Scalar &rhoE);
    
    /**
     * @brief 用给定原始量计算守恒量
     * 
     * @param[in, out] rho 单元密度值
     * @param[in] U 单元速度值
     * @param[in] p 单元压强值
     * @param[out] rhoU 单元动量值
     * @param[out] rhoE 单元能量值
     */
    void CalcuateConservedValue(const Scalar &rho, const Vector &U, const Scalar &p, Vector &rhoU, Scalar &rhoE);

    /**
    * @brief 根据湍流名称返回相应的湍流场指针
    *
    * @param[in] macro 湍流名称宏
    */
    ElementField<Scalar>* GetTurbulencePointer(const FlowMacro::Scalar &macro);

    /**
     * @brief 计算总粘性系数（层流+湍流）
     * 
     * @param[in] elementID 单元编号
     * @return Scalar 
     */
    Scalar CalculateMu(const int &elementID);

    /**
     * @brief 计算总热传导系数（层流+湍流）
     * 
     * @param[in] elementID 单元编号
     * @return Scalar 
     */
    Scalar CalculateKappa(const int &elementID);

    /**
     * @brief 计算应力张量
     * 
     * @param[in] mu 粘性系数
     * @param[in] gradientU 速度梯度
     * @return Tensor 
     */
    Tensor CalculateTau(const Scalar &mu, const Tensor &gradientU);
    
    /**
     * @brief 更新当前步其他衍生物理场并存储在流场包中
     * 
     */
    void UpdateExtrasField();

    /**
     * @brief 计算压力探测器
     * 
     */
    void CalculatePressureSwitch();

    /**
     * @brief 更新物理时间步长及当前物理时间
     * 
     * @param[in, out] timeStep 物理时间步长
     */
    void UpdateCurrentTime(const Scalar &timeStep);

    /**
     * @brief 初始化流场
     * 
     * @param[in] initialType 初始化方法
     */
    void InitializeField(const Initialization::Type &initialType);

    /**
     * @brief 获取当前网格计算采用的CFL数
     * 
     */
    Scalar &GetCFLNumber() { return this->CFLNumber; }

    /**
     * @brief 设置当前网格计算采用的CFL数
     * 
     * @param[in] CFLNumber_ 物理时间步长
     */
    void SetCFLNumber(const Scalar &CFLNumber_) { this->CFLNumber = CFLNumber_; }

    /**
     * @brief 设置Jacobain矩阵更新状态
     * 
     * @param[in] flag Jacobian是否更新标识
     */
    void SetUpdateJacobian(const bool flag) { this->implicitSolver.updateJacobian = flag; }

	/**
	* @brief 设置非定常续算起始物理时间
	*
	* @param[in] startTime_ 非定常续算起始物理时间
	*/
	void SetStartTime(const Scalar &startTime_) { this->unsteadyStatus.startTime = startTime_; }

#if defined(_EnableFlutter_)
	/**
	* @brief 获取物面摄动场数据
	*
	* @return const std::vector<std::vector<Vector>>& 物面摄动场向量
	*/
	const std::vector<std::vector<Vector>>& GetWallPerturbationFields() const { return fieldPointer.wallPerturbationFields; }

	/**
	* @brief 获取物面节点ID映射
	*
	* @return const std::vector<int>& 物面节点ID映射
	*/
	const std::vector<int>& GetWallNodeMapping() const { return wallNodeMapping; }

	/**
	* @brief 初始化摄动场数据
	*
	* @param perturbationData 摄动场数据
	*/
	void InitializePerturbationFields(const std::vector<std::vector<Vector>>& perturbationData);

private:
	/**
	* @brief 从全场摄动场中提取物面摄动场
	*/
	void ExtractWallPerturbationFields();

public:
#endif

private:
    /**
     * @brief 建立各种场指针并开辟空间
     * 
     */
    void SetFieldPointer();

    /**
     * @brief 设置湍流相关状态量
     * 
     */
    void SetTurbulentStatus();

    /**
     * @brief 设置非定常计算相关状态量
     * 
     */
    void SetUnsteadyStatus();

#if defined(_EnableMultiSpecies_)
	/**
	* @brief 设置多组分相关状态量
	*
	*/
	void SetMultiSpeciesStatus();
#endif

    /**
    * @brief 预计算扩散项计算需要的几何信息
    *
    */
    void PreCalculate();

    /**
    * @brief 根据全局物理场初始化
    *
    * @param[out] phiLocal 当地物理场
    * @param[in] fieldName 全局物理场名称
    */
    template<class Type>
    void InitializeFromGlobal(ElementField<Type> &phiLocal, const std::string &fieldName);

    /**
    * @brief 初始化非定常计算其它时间步流场
    *
    */
    void InitializeUnsteadyField();

private:
    Configure::Flow::FlowConfigure &flowConfigure; ///< 流场参数
    MeshStruct meshStruct; ///< 网格结构体
    Material::Flow::Materials material; ///< 流体的材料属性类对象

    FieldPointer fieldPointer; ///< 当前步的物理场
    FieldOldPointer field0Pointer; ///< 上一步的物理场
    FieldOldPointer field0PointerBackup; ///< 上一步物理场备份（用于多重网格延拓）
    std::vector<FieldOldPointer> unsteadyFieldPointer; ///< 非定常物理场
    GradientFieldPointer gradientFieldPointer; ///< 当前步的梯度场
    ResidualFieldPointer residualFieldPointer; ///< 当前步的残值场
    ImplicitSolver implicitSolver; ///< 隐式求解Jacobian矩阵等信息

    MaterialNumber materialNumber; ///< 材料数据信息
    TurbulentStatus turbulentStatus; ///< 湍流信息
    UnsteadyStatus unsteadyStatus; ///< 非定常信息

    ExtraInfo extraInfo; ///<  流场计算需要的辅助信息

    std::vector<Boundary::Type> flowBoundaryType; ///< 存放边界条件类型的容器

    ZoneManager *zoneManager; ///<多域管理器，暂时存放，后续需要挪出

    std::shared_ptr<MRFZONE> mrf;///<  MRF数据

    Scalar CFLNumber; ///< 当前网格计算采用的CFL数

#if defined(_EnableFlutter_)
    std::vector<int> wallNodeMapping; ///< 物面节点ID映射
#endif

#if defined(_EnableMultiSpecies_)
	MultiSpeciesStatus multiSpeciesStatus; ///< 组分状态参数
#endif
};

} // namespace Package

#endif