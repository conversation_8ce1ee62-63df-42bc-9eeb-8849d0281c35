# 颤振计算框架修改说明

## 概述
本次修改将颤振计算框架从物理位移/力系统改为广义位移/力系统，基于模态坐标进行结构动力学计算。**关键改进：实现了真正的GetWallPerturbationFields()方法，从全场摄动场中正确提取物面摄动场数据作为模态振型，通过壁面气动力与物面摄动场的点积计算广义气动力。**

## 主要修改内容

### 1. 新增文件
- `sourceFlow/flowSolver/StructuralDynamics.h` - 结构动力学类头文件
- `sourceFlow/flowSolver/StructuralDynamics.cpp` - 结构动力学类实现
- `modal_data_example.dat` - 模态数据文件示例

### 2. 修改的文件

#### sourceFlow/flowSolver/OuterLoop.h
- 添加了 `#include "sourceFlow/flowSolver/StructuralDynamics.h"`
- 修改了 `ComputeAerodynamicForces()` 为 `ComputeGeneralizedAerodynamicForces()`
- 添加了 `ConvertGeneralizedToPhysicalDisplacement()` 方法

#### sourceFlow/flowSolver/OuterLoop.cpp
- 修改了颤振计算流程，使用广义坐标系统
- 实现了 `ComputeGeneralizedAerodynamicForces()` 方法
- 实现了 `ConvertGeneralizedToPhysicalDisplacement()` 方法
- 添加了 `InitializeFlutterCalculation()` 方法
- 修改了强耦合和弱耦合的计算逻辑
- 添加了 `LoadPerturbationFields()` 方法，从分区摄动场文件中读取数据

#### sourceFlow/configure/FlowConfigure.h
- 添加了 `FlutterStruct` 结构体
- 添加了颤振参数的访问方法 `GetFlutter()` 和 `SetFlutter()`

#### sourceFlow/package/FlowPackage.h
- 添加了全场摄动场存储结构 `std::vector<ElementField<Vector>*> perturbationFields`
- 添加了物面摄动场存储结构 `std::vector<std::vector<Vector>> wallPerturbationFields`
- 添加了物面节点映射 `std::vector<int> wallNodeMapping`
- 实现了真正的 `GetWallPerturbationFields()` 方法
- 添加了 `InitializePerturbationFields()` 和 `ExtractWallPerturbationFields()` 方法

#### sourceFlow/package/FlowPackage.cpp
- 实现了从全场摄动场中提取物面摄动场的功能
- 实现了物面节点的自动识别和映射
- 确保只有壁面边界上的节点被包含在物面摄动场中

#### meshProcess/motion/MotionManager.h
- 添加了 `UpdateFlutterMesh()` 方法（兼容性）
- 添加了 `UpdateFlutterMeshWithDisplacements()` 方法

#### meshProcess/motion/MotionManager.cpp
- 实现了颤振网格更新方法

## 技术特点

### 1. 广义坐标系统
- 使用模态坐标 {q} 代替物理位移
- 求解广义运动方程：[M]{q̈} + [C]{q̇} + [K]{q} = {Q}
- 广义质量、刚度、阻尼矩阵为对角矩阵（正交模态）

### 2. 气动力计算
- **全场摄动场读取**：从分区摄动场文件中读取全场数据并存储为ElementField<Vector>
- **物面摄动场提取**：自动识别壁面边界，从全场摄动场中提取物面部分
- **节点映射建立**：建立物面节点ID到物面摄动场索引的映射关系
- 计算物理气动力后与物面模态振型做点积得到广义气动力
- 公式：Q_i = Σ(F_aero · φ_i)
- 其中 φ_i 是从物面摄动场中获取的第i阶模态振型
- **面平均处理**：对于面单元，使用面节点的物面摄动场平均值
- **MPI并行支持**：自动进行全局求和以获得完整的广义气动力

### 3. 位移转换
- **基于摄动场的模态叠加**：直接使用摄动场数据进行模态叠加
- 通过模态叠加将广义位移转换为物理位移
- 公式：u = Σ(q_i × φ_i)
- 其中 φ_i 直接从摄动场中获取，无需插值计算

### 4. 时间积分
- 使用Newmark-β方法求解广义运动方程
- 支持自定义β和γ参数
- 默认值：β=0.25, γ=0.5（平均加速度法）

## 配置参数

### FlutterStruct 参数说明
```cpp
struct FlutterStruct
{
    bool enableFlutter;           // 是否启用颤振计算
    bool strongCoupling;          // 是否使用强耦合
    int maxFSIIterations;         // 最大FSI迭代次数
    double fsiTolerance;          // FSI收敛容差
    std::vector<double> naturalFrequencies; // 各模态固有频率 (rad/s)
    std::vector<double> dampingRatios; // 各模态阻尼比
    double newmarkBeta;           // Newmark-β参数
    double newmarkGamma;          // Newmark-γ参数
};
```

**重要说明**：
- 不再需要模态数据文件，直接使用摄动场数据
- `naturalFrequencies` 需要在配置中指定各模态的固有频率
- 如果未指定频率，系统将使用默认值（10, 20, 30... rad/s）

## 摄动场数据要求

**不再需要单独的模态数据文件**，系统直接使用已加载的摄动场数据：

1. **摄动场文件**：按照现有的摄动场格式准备文件
2. **固有频率配置**：在配置文件中指定各模态的固有频率
3. **自动识别**：系统自动从摄动场中获取模态数量和振型数据

## 使用方法

1. **准备摄动场文件**：按照现有格式准备摄动场文件
2. **配置固有频率**：在配置文件中设置各模态的固有频率
3. **设置颤振参数**：在配置文件中设置其他颤振计算参数
4. **编译启用**：编译时启用 `_EnableFlutter_` 宏定义
5. **运行计算**：系统自动从摄动场中获取模态数据并进行颤振计算

## 优势

1. **计算效率高**：只需求解少数几个模态的运动方程
2. **数值稳定性好**：避免了大规模矩阵求解
3. **物理意义清晰**：直接基于结构模态特性
4. **易于扩展**：可方便添加更多模态或修改阻尼模型
5. **无需额外文件**：直接使用现有的摄动场数据，无需准备单独的模态文件
6. **MPI并行优化**：自动处理分布式计算中的广义力求和
7. **数据一致性**：摄动场数据与CFD网格完全匹配，避免插值误差

## 注意事项

1. **摄动场数据**：确保摄动场文件正确加载，包含所需的模态振型数据
2. **固有频率配置**：必须在配置文件中正确设置各模态的固有频率
3. **模态数量匹配**：配置的频率数量必须与摄动场数量一致
4. **MPI环境**：在并行计算环境中，系统自动处理数据同步
5. **数据格式**：摄动场数据格式必须与CFD网格兼容

## 后续开发建议

1. **摄动场验证**：添加摄动场数据的自动验证功能
2. **频率自动提取**：从摄动场文件头部自动读取固有频率信息
3. **阻尼模型扩展**：支持更多的结构阻尼模型（如瑞利阻尼）
4. **颤振分析**：添加颤振稳定性分析和临界速度计算功能
5. **耦合优化**：优化FSI耦合算法的收敛性和稳定性
6. **后处理增强**：添加广义坐标和模态响应的可视化输出
